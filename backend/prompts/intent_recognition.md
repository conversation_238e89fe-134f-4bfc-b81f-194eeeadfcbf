# 意图识别提示词模板 (版本 v4)

## ！！！严格输出格式要求！！！
⚠️ 只允许输出唯一一行纯 JSON 对象，禁止包含：
  • 任何额外文本、说明、解释、注释
  • 代码块标记（如 ```json）
  • 多余的空格、换行、标点
  • 额外字段、顺序错误

✅ 唯一可接受格式：
{
  "intent": "provide_information",
  "sub_intent": "answer_question",
  "emotion": "neutral",
  "confidence": 0.92,
  "entities": {}
}

## 意图识别任务
请根据下面的用户输入，分析用户的意图、子意图和情感。

## 当前会话状态
{current_state}

## 用户输入
{user_input}

## 对话历史
{full_conversation}

## 🔥 状态感知规则（优先级最高）
**根据当前会话状态调整意图识别策略：**

### 当current_state为"COLLECTING_INFO"时：
- **优先识别为process_answer**：用户很可能在回答系统提出的问题
- 除非用户明确表示要重新开始（如"新聊天"、"重新开始"、"新需求"）
- 或者用户明确提出新的不相关问题

### 当current_state为"IDLE"时：
- 优先识别business_requirement：用户描述新的业务需求
- 正常应用所有意图识别规则

### 当current_state为"DOCUMENTING"时：
- 优先识别文档相关操作：confirm、modify、complete等

## 基础意图类型（intent，必选其一）

**重要：优先识别业务需求！**

- **restart**: 用户要求重新开始或重置对话
  * 示例："重新开始"、"重来"、"重置"

- **confirm**: 用户确认或同意某个操作
  * 示例："确认"、"好的"、"同意"

- **business_requirement**: 用户描述具体的业务需求、项目需求或想要实现的目标
  * 示例："我想策划营销活动"、"需要开发一个网站"、"想做个APP"、"需要设计Logo"
  * 这是最重要的意图类型，当用户描述任何具体的业务目标时都应该选择此项

- **emotional_support**: 用户表达情感困扰或需要心理支持
  * 示例："我很焦虑"、"感觉很困惑"、"不知道怎么办"

- **ask_question**: 用户询问系统功能、流程或一般性问题
  * 示例："你能做什么"、"怎么使用"、"有什么功能"

- **domain_specific_query**: 用户询问特定领域的专业知识
  * 示例："什么是UI设计"、"营销策略有哪些"、"软件开发流程"

- **process_query**: 用户询问系统流程或操作步骤
  * 示例："流程是什么"、"怎么操作"、"步骤是什么"

- **system_capability_query**: 用户询问系统的具体能力和功能
  * 示例："你能帮我做什么"、"有哪些功能"、"支持什么服务"

- **greeting**: 用户的问候或打招呼
  * 示例："你好"、"早上好"、"hi"

- **provide_information**: 用户提供具体信息或回答问题
  * 示例："我的预算是1万元"、"我们公司是做教育的"、"目标用户是学生"

- **process_answer**: 处理用户在信息收集阶段的回答
  * 示例："我的需求是..."、"具体来说..."、"我希望..."

- **request_clarification**: 用户请求澄清或解释某些概念
  * 示例："什么意思"、"不太明白"、"能解释一下吗"、"能简单描述一下每种风格的特点吗"、"这些选项有什么区别"、"能详细说明一下吗"

- **feedback**: 用户提供反馈或评价
  * 示例："很好"、"不错"、"有问题"

- **general_chat**: 用户进行一般性对话或闲聊
  * 示例："今天天气真好"、"你觉得怎么样"、"随便聊聊"

- **modify**: 用户要求修改某些内容
  * 示例："修改"、"改一下"、"调整"

- **unknown**: 无法识别的用户意图

## 子意图类型（sub_intent，根据主意图选择）

### 当intent为business_requirement时
- software_development: 软件开发需求（如网站、APP、系统开发等）
- design_requirement: 设计需求（如UI设计、品牌设计、产品设计等）
- marketing_requirement: 营销需求（如营销策划、推广活动、品牌推广等）
- business_process: 业务流程需求（如流程优化、管理制度等）
- consulting_requirement: 咨询需求（如战略咨询、技术咨询等）
- content_creation: 内容创作需求（如文案、视频、图片等）
- event_planning: 活动策划需求（如会议、培训、活动组织等）
- other_business: 其他业务需求

### 当intent为provide_information时
- answer_question: 回答系统提出的问题
- supplement_info: 主动补充额外信息
- correct_info: 纠正之前提供的信息
- elaborate_details: 详细阐述某个方面
- provide_example: 提供具体示例或案例
- provide_preference: 表达个人偏好或选择

### 当intent为ask_question时
- requirement_question: 关于需求收集过程的问题
- domain_knowledge_question: 关于特定领域知识的问题
- technical_question: 关于技术实现的问题
- scope_question: 关于项目范围的问题
- timeline_question: 关于时间线或进度的问题
- cost_question: 关于成本或资源的问题

### 当intent为request_clarification时
- term_clarification: 请求解释专业术语
- question_clarification: 请求解释系统提问的含义
- process_clarification: 请求解释流程步骤
- option_clarification: 请求解释可选项的区别
- implication_clarification: 请求解释某选择的影响

### 当intent为domain_specific_query时
- business_domain_query: 业务领域相关询问
- technical_domain_query: 技术领域相关询问
- industry_standard_query: 行业标准相关询问
- best_practice_query: 最佳实践相关询问

### 当intent为process_query时
- workflow_query: 工作流程相关询问
- methodology_query: 方法论相关询问
- step_sequence_query: 步骤顺序相关询问
- milestone_query: 里程碑相关询问

### 当intent为system_capability_query时
- feature_capability_query: 功能能力询问
- limitation_query: 系统限制询问
- integration_capability_query: 集成能力询问
- performance_query: 性能相关询问

### 当intent为feedback时
- positive_feedback: 积极反馈
- negative_feedback: 消极反馈
- suggestion_feedback: 建议性反馈
- correction_feedback: 纠正性反馈

## 情感类型（emotion，必选其一）
- positive: 积极、满意、高兴
- negative: 消极、不满、沮丧
- neutral: 中性、平静
- anxious: 焦虑、担忧
- confused: 困惑、不确定

## 识别示例

**示例1：营销需求**
用户输入："我想策划线上营销活动，获得更多客户"
正确输出：{"intent": "business_requirement", "sub_intent": "marketing_requirement", "emotion": "neutral", "confidence": 0.9, "entities": {}}

**示例2：开发需求**
用户输入："我需要开发一个电商网站"
正确输出：{"intent": "business_requirement", "sub_intent": "software_development", "emotion": "neutral", "confidence": 0.9, "entities": {}}

**示例3：设计需求**
用户输入："帮我设计一个Logo"
正确输出：{"intent": "business_requirement", "sub_intent": "design_requirement", "emotion": "neutral", "confidence": 0.9, "entities": {}}

## 字段要求
{
  "intent": "必须从上述基础意图类型中选择",
  "sub_intent": "根据主意图从相应子意图列表中选择，如无明确子意图可留空",
  "emotion": "必须从上述情感类型中选择",
  "confidence": "0.0-1.0之间的数值",
  "entities": "空对象{}或实体字典"
}


