"""
对话流程状态管理器

负责管理：
- 关注点（Focus Points）状态
- 会话状态缓存
- 状态转换逻辑
"""

import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from backend.config.unified_config_loader import get_unified_config


class StateManager:
    """状态管理器"""

    def __init__(self, db_manager, focus_point_manager):
        self.db_manager = db_manager
        self.focus_point_manager = focus_point_manager
        self.focus_points_status_cache = {}
        self.logger = logging.getLogger(__name__)
    
    async def initialize_focus_points(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        初始化关注点状态 - 直接使用FocusPointManager

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            bool: 初始化是否成功
        """
        return await self.focus_point_manager.initialize_focus_points(session_id, user_id, focus_points)

    async def update_focus_point_status(self, session_id: str, user_id: str, point_id: str, status: str, value: Optional[str] = None) -> bool:
        """
        更新关注点状态 - 直接使用FocusPointManager并同步更新缓存

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID
            status: 新状态
            value: 可选的值

        Returns:
            bool: 更新是否成功
        """
        # 确保value是字符串类型，避免数据库绑定错误
        if value is not None:
            if isinstance(value, dict):
                value = json.dumps(value, ensure_ascii=False)
            elif not isinstance(value, str):
                value = str(value)

        # 更新数据库
        success = await self.focus_point_manager.update_focus_point_status(session_id, user_id, point_id, status, value)

        # 如果数据库更新成功，同步更新缓存以保持一致性
        if success:
            self.focus_points_status_cache[point_id] = {
                "status": status,
                "value": value,
                "attempts": 0,
                "is_covered": status == "completed",
                "updated_at": datetime.now().isoformat()
            }

        return success

    async def get_next_pending_point(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取下一个待处理的关注点

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            Optional[Dict[str, Any]]: 下一个待处理的关注点，如果没有则返回None
        """
        if not session_id:
            return None

        # 从FocusPointManager获取状态
        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)

        # 优先返回P0/P1级别的未处理关注点
        for point in sorted(focus_points, key=lambda x: x["priority"]):
            point_id = point["id"]
            status = statuses.get(point_id, {}).get("status", "pending")
            if status == "pending" and point["priority"] in ["P0", "P1"]:
                return point

        # 其次返回P2级别的未处理关注点
        for point in focus_points:
            point_id = point["id"]
            status = statuses.get(point_id, {}).get("status", "pending")
            if status == "pending":
                return point

        return None

    async def get_processing_point(self, session_id: str, user_id: str) -> Optional[str]:
        """
        获取当前正在处理的关注点ID

        Returns:
            Optional[str]: 正在处理的关注点ID，如果没有则返回None
        """
        if not session_id:
            return None

        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)
        self.logger.info(f"[DEBUG] get_processing_point - 加载的状态数量: {len(statuses)}")

        processing_points = []
        for point_id, status_info in statuses.items():
            current_status = status_info.get("status")
            self.logger.info(f"[DEBUG] 关注点 {point_id} 状态: {current_status}")
            if current_status == "processing":
                processing_points.append(point_id)

        if processing_points:
            if len(processing_points) > 1:
                self.logger.warning(f"发现多个processing状态的关注点: {processing_points}，返回第一个")
            self.logger.info(f"[DEBUG] 找到processing关注点: {processing_points[0]}")
            return processing_points[0]

        self.logger.info(f"[DEBUG] 未找到processing状态的关注点")
        return None

    async def clear_all_processing_status(self, session_id: str, user_id: str) -> None:
        """
        清理所有processing状态的关注点

        将所有处于processing状态的关注点重置为pending状态
        """
        if not session_id:
            return

        try:
            # 使用高效的批量更新查询，一次性清理所有processing状态
            await self.db_manager.execute_update(
                get_unified_config().get_database_query("focus_points.clear_processing"),
                (session_id, user_id)
            )
            self.logger.debug(f"清理所有processing状态完成 - session_id: {session_id}, user_id: {user_id}")
        except Exception as e:
            self.logger.error(f"清理processing状态失败: {e}", exc_info=True)

    async def set_point_processing(self, session_id: str, user_id: str, point_id: str) -> bool:
        """
        安全地设置关注点为processing状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID

        Returns:
            bool: 设置是否成功
        """
        await self.clear_all_processing_status(session_id, user_id)
        return await self.update_focus_point_status(session_id, user_id, point_id, "processing")

    async def set_point_processing_safely(self, session_id: str, user_id: str, point_id: str) -> bool:
        """
        安全设置processing状态（别名方法，保持与文档一致）

        Args:
            session_id: 会话ID
            user_id: 用户ID
            point_id: 关注点ID

        Returns:
            bool: 设置是否成功
        """
        return await self.set_point_processing(session_id, user_id, point_id)

    async def check_all_required_covered(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        检查P0/P1关注点是否全部完成

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            bool: 所有必需关注点是否都已完成
        """
        if not focus_points:
            return False

        statuses = await self.focus_point_manager.load_focus_points_status(session_id, user_id)

        # 检查所有P0/P1级别的关注点是否都已完成
        for point in focus_points:
            if point.get("priority") in ["P0", "P1"]:
                point_id = point["id"]
                status_info = statuses.get(point_id, {})
                status = status_info.get("status", "pending")
                if status != "completed":
                    return False
        return True

    def get_focus_point_status(self, point_id: str) -> Dict[str, Any]:
        """
        获取关注点状态 - 从缓存返回

        Args:
            point_id: 关注点ID

        Returns:
            Dict[str, Any]: 关注点状态信息
        """
        return self.focus_points_status_cache.get(point_id, {})

    async def reset_focus_points(self, session_id: str, user_id: str) -> None:
        """
        重置关注点状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
        """
        await self.focus_point_manager.reset_focus_points_status(session_id, user_id)
        self.focus_points_status_cache = {}