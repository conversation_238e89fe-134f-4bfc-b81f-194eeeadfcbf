#!/usr/bin/env python3
"""
复合意图处理功能测试脚本
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

async def test_composite_handler():
    """测试复合意图处理功能"""
    try:
        # 导入必要的模块
        from backend.handlers.composite_handler import CompositeHandler
        from backend.agents.conversation_flow import ConversationFlowAgent
        from backend.config.unified_config_loader import get_unified_config

        print("✅ 模块导入成功")

        # 初始化组件
        config = get_unified_config()
        conversation_flow = ConversationFlowAgent()
        handler = CompositeHandler(conversation_flow=conversation_flow)
        
        print("✅ 组件初始化成功")
        
        # 测试复合意图识别
        test_message = "用户注册、商品展示、在线支付。你们平台支持哪些支付？"
        print(f"🧪 测试消息: {test_message}")
        
        # 测试意图识别
        has_requirement, has_question = await handler._detect_composite_intent(test_message)
        print(f"📊 意图识别结果: 需求={has_requirement}, 问题={has_question}")
        
        if has_requirement and has_question:
            print("✅ 复合意图识别成功！")
            
            # 测试问题提取
            question = await handler._extract_question_with_llm(test_message)
            print(f"❓ 提取的问题: {question}")
            
            if question:
                print("✅ 问题提取成功！")
            else:
                print("⚠️ 问题提取失败")
                
        else:
            print("❌ 复合意图识别失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

if __name__ == "__main__":
    print("🚀 开始测试复合意图处理功能...")
    success = asyncio.run(test_composite_handler())
    if success:
        print("🎉 测试完成！")
    else:
        print("💥 测试失败！")
